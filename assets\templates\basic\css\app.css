body{
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    min-height: 100vh; 
}
.footer {
    margin-top: auto; 
}
.custom-radius-10 {
    border-radius: 10px !important;  
}
.border--primary {
    border-left: 5px solid  #4634ff !important;
}
.custom--card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 0px solid rgba(0, 0, 0, 0);
    border-radius: .25rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 6px 0 rgb(218 218 253 / 65%), 0 2px 6px 0 rgb(206 206 238 / 54%);
}
.widgets-icons-2 {
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
    font-size: 27px;
    border-radius: 10px;
}
.rounded--circle {
    border-radius: 50%!important;
}
.text--white {
    color: #fff!important;
}
.ms--auto {
    margin-left: auto!important;
}
.widgets-icons-2 i{
    color: #4634ff;
}
.body-bg{
    background-color: #fbfbfb;
}
.custom-border-top-success{
    border-top: 3px #198754 solid;
}
.custom-border-top-info{
    border-top: 3px #0dcaf0 solid;
}
.custom-border-top-warning{
    border-top: 3px #ffcd39 solid;
}
.custom-border-top-primary{
    border-top: 3px #4634ff solid;
}
.has-anchor {
    position: absolute;
    inset: 0;
    z-index: 1;
}
