<form action="" class="form" id="domain-search-form">
    <div class="form-group position-relative mb-0">
        <div class="domain-search-icon"><i class="fas fa-search"></i></div>
        <input class="form-control form--control" type="text" name="domain" required placeholder="@lang('Domain name or keyword')" value="{{ @request()->domain }}">
        <div class="domain-search-icon-reset">
            <button class="btn btn--base" type="submit" id="search-btn">
                <span class="btn-text">@lang('Search')</span>
                <span class="btn-loading d-none">
                    <i class="fas fa-spinner fa-spin"></i> @lang('Searching...')
                </span>
            </button>
        </div>
    </div>
</form>

<!-- Domain Results Container -->
<div id="domain-results" class="mt-4" style="display: none;">
    <div id="domain-status-message" class="text-center mb-3"></div>
    <div id="domain-list"></div>
</div>

@push('style')
    <style>
        .domain-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fff;
            transition: all 0.3s ease;
        }

        .domain-row:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-color: #007bff;
        }

        .domain-row span:first-child {
            font-weight: 600;
            font-size: 16px;
        }

        #domain-results {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .btn-loading .fa-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
@endpush

@push('script')
    <script>
        (function($) {
            "use strict";

            $('#domain-search-form').on('submit', function(e) {
                e.preventDefault();

                var domain = $(this).find('input[name=domain]').val().trim();
                if (!domain) {
                    return;
                }

                // Show loading state
                var $btn = $('#search-btn');
                var $btnText = $btn.find('.btn-text');
                var $btnLoading = $btn.find('.btn-loading');

                $btn.prop('disabled', true);
                $btnText.addClass('d-none');
                $btnLoading.removeClass('d-none');

                // Clear previous results
                $('#domain-results').hide();
                $('#domain-status-message').empty();
                $('#domain-list').empty();

                // Make AJAX request
                $.ajax({
                    url: "{{ route('search.domain') }}",
                    method: 'POST',
                    data: {
                        domain: domain,
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        if (response.success) {
                            displayDomainResults(response.result);
                        } else {
                            showError(response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = '@lang("An error occurred while searching for domains. Please try again.")';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        showError(errorMessage);
                    },
                    complete: function() {
                        // Reset button state
                        $btn.prop('disabled', false);
                        $btnText.removeClass('d-none');
                        $btnLoading.addClass('d-none');
                    }
                });
            });

            function displayDomainResults(result) {
                var data = result.data;
                var domain = result.domain;
                var isSupported = result.isSupported;

                // Show results container
                $('#domain-results').show();

                // Display status message
                var statusHtml = '';
                if (data && data.length > 0) {
                    var searchedDomain = data.find(function(item) {
                        return item.domain === domain;
                    });

                    if (searchedDomain) {
                        if (searchedDomain.available) {
                            statusHtml = '<h3>@lang("Congratulations")! <span class="text--base">' + domain + ' @lang("is")</span> @lang("available")!</h3>';
                        } else {
                            statusHtml = '<h3><span class="text--danger">' + domain + ' @lang("is")</span> @lang("unavailable")!</h3>';
                        }
                    }
                }

                if (!isSupported && domain) {
                    statusHtml += '<h3>@lang("We are not supporting") <span class="text--warning">(' + result.tld + ')</span> @lang("right now")</h3>';
                }

                $('#domain-status-message').html(statusHtml);

                // Display domain list
                if (data && data.length > 0) {
                    var domainsHtml = '';

                    // Sort by match (searched domain first)
                    data.sort(function(a, b) {
                        return (b.match || 0) - (a.match || 0);
                    });

                    data.forEach(function(domainData) {
                        domainsHtml += '<div class="domain-row">';
                        domainsHtml += '<span>' + domainData.domain + '</span>';
                        domainsHtml += '<div class="text-end">';

                        if (domainData.available && domainData.setup) {
                            var price = domainData.setup.pricing && domainData.setup.pricing.firstPrice
                                ? domainData.setup.pricing.firstPrice.price
                                : 0;

                            domainsHtml += '<span class="fw-bold text-end">';
                            domainsHtml += '{{ $general->cur_sym ?? "$" }}' + parseFloat(price).toFixed(2);
                            domainsHtml += '</span>';

                            domainsHtml += '<form action="{{ route("shopping.cart.add.domain") }}" method="post" class="d-inline ms-2">';
                            domainsHtml += '@csrf';
                            domainsHtml += '<input type="hidden" name="domain" value="' + domainData.domain + '">';
                            domainsHtml += '<input type="hidden" name="domain_setup_id" value="' + domainData.setup.id + '">';

                            var btnClass = 'btn btn--sm btn--base';
                            if (domainData.domain !== domain) {
                                btnClass += '-outline';
                            }

                            domainsHtml += '<button class="' + btnClass + '">';
                            domainsHtml += '<i class="la la-cart-plus"></i> @lang("Add")';
                            domainsHtml += '</button>';
                            domainsHtml += '</form>';
                        } else {
                            domainsHtml += '<span class="text--info fw-bold">@lang("Unavailable")</span>';
                        }

                        domainsHtml += '</div>';
                        domainsHtml += '</div>';
                    });

                    $('#domain-list').html(domainsHtml);
                }
            }

            function showError(message) {
                $('#domain-results').show();
                var errorHtml = '<div class="alert alert-danger">';
                if (Array.isArray(message)) {
                    message.forEach(function(msg) {
                        errorHtml += '<p class="mb-0">' + msg + '</p>';
                    });
                } else {
                    errorHtml += '<p class="mb-0">' + message + '</p>';
                }
                errorHtml += '</div>';
                $('#domain-status-message').html(errorHtml);
            }

        })(jQuery);
    </script>
@endpush
