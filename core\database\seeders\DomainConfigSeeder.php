<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DomainRegister;
use App\Models\DomainSetup;
use App\Models\DomainPricing;

class DomainConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create a default domain register (Namecheap)
        $namecheapRegister = DomainRegister::firstOrCreate(
            ['alias' => 'Namecheap'],
            [
                'name' => 'Namecheap',
                'alias' => 'Namecheap',
                'params' => json_encode([
                    'username' => ['name' => 'Username', 'value' => ''],
                    'api_key' => ['name' => 'API Key', 'value' => ''],
                    'sandbox_username' => ['name' => 'Sandbox Username', 'value' => ''],
                ]),
                'test_mode' => 1,
                'status' => 1,
                'default' => 1
            ]
        );

        // Create a Resell domain register
        $resellRegister = DomainRegister::firstOrCreate(
            ['alias' => 'Resell'],
            [
                'name' => 'Resell',
                'alias' => 'Resell',
                'params' => json_encode([
                    'auth_user_id' => ['name' => 'Auth User ID', 'value' => ''],
                    'api_key' => ['name' => 'API Key', 'value' => ''],
                ]),
                'test_mode' => 1,
                'status' => 0,
                'default' => 0
            ]
        );

        // Create common domain extensions
        $extensions = [
            '.com' => ['price' => 12.99, 'renew' => 12.99],
            '.net' => ['price' => 14.99, 'renew' => 14.99],
            '.org' => ['price' => 13.99, 'renew' => 13.99],
            '.info' => ['price' => 11.99, 'renew' => 11.99],
            '.biz' => ['price' => 15.99, 'renew' => 15.99],
        ];

        foreach ($extensions as $extension => $pricing) {
            $domainSetup = DomainSetup::firstOrCreate(
                ['extension' => $extension],
                [
                    'extension' => $extension,
                    'id_protection' => 1,
                    'status' => 1
                ]
            );

            // Create pricing for this domain setup
            DomainPricing::firstOrCreate(
                ['domain_id' => $domainSetup->id],
                [
                    'domain_id' => $domainSetup->id,
                    'one_year_price' => $pricing['price'],
                    'one_year_whois_protection' => 2.99,
                    'one_year_renew' => $pricing['renew'],
                    'two_year_price' => $pricing['price'] * 2,
                    'two_year_whois_protection' => 2.99 * 2,
                    'two_year_renew' => $pricing['renew'] * 2,
                    'three_year_price' => $pricing['price'] * 3,
                    'three_year_whois_protection' => 2.99 * 3,
                    'three_year_renew' => $pricing['renew'] * 3,
                    'four_year_price' => $pricing['price'] * 4,
                    'four_year_whois_protection' => 2.99 * 4,
                    'four_year_renew' => $pricing['renew'] * 4,
                    'five_year_price' => $pricing['price'] * 5,
                    'five_year_whois_protection' => 2.99 * 5,
                    'five_year_renew' => $pricing['renew'] * 5,
                    'six_year_price' => $pricing['price'] * 6,
                    'six_year_whois_protection' => 2.99 * 6,
                    'six_year_renew' => $pricing['renew'] * 6,
                ]
            );
        }

        echo "Domain configuration seeded successfully!\n";
        echo "Created " . count($extensions) . " domain extensions\n";
        echo "Default domain register: " . $namecheapRegister->name . "\n";
    }
}
