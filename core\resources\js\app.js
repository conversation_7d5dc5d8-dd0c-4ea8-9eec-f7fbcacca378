// Main Application Object - SPA Enhanced
window.AchidasApp = {
    // Configuration
    config: {
        baseUrl: window.location.origin,
        apiUrl: '/user',
        csrfToken: $('meta[name="csrf-token"]').attr('content'),
        loadingDelay: 300,
        notificationDuration: 5000,
        currentPage: 'dashboard',
        pageHistory: [],
        contentContainer: '#main-content',
        sidebarContainer: '.sidebar-nav'
    },

    // Initialize the application
    init: function(config) {
        // Merge provided config with defaults
        if (config) {
            Object.assign(this.config, config);
        }

        this.setupCSRF();
        this.setupEventListeners();
        this.setupNotifications();
        this.setupModals();
        this.setupTables();
        this.setupForms();
        this.setupNavigation();
        this.initializeAnimations();
        this.initializeSPA();
        console.log('Achidas App initialized successfully');
    },

    // Setup CSRF token for all AJAX requests
    setupCSRF: function() {
        // Get CSRF token from meta tag or config
        const csrfToken = this.config.csrfToken || $('meta[name="csrf-token"]').attr('content');

        if (csrfToken) {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
        }
    },

    // Initialize SPA functionality
    initializeSPA: function() {
        const self = this;
        
        // Handle browser back/forward buttons
        window.addEventListener('popstate', function(event) {
            if (event.state && event.state.page) {
                self.loadPage(event.state.page, false);
            }
        });

        // Set initial state
        const currentPath = window.location.pathname;
        const pageName = this.extractPageFromPath(currentPath);
        history.replaceState({page: pageName}, '', currentPath);
        this.config.currentPage = pageName;
    },

    // Extract page name from URL path
    extractPageFromPath: function(path) {
        const segments = path.split('/').filter(segment => segment);
        if (segments.length >= 2 && segments[0] === 'user') {
            return segments[1] || 'dashboard';
        }
        return 'dashboard';
    },

    // Setup navigation event listeners
    setupNavigation: function() {
        const self = this;
        
        // Handle sidebar navigation clicks
        $(document).on('click', '.nav-link[data-page]', function(e) {
            e.preventDefault();
            const page = $(this).data('page');
            const url = $(this).attr('href');
            
            if (page && page !== self.config.currentPage) {
                self.loadPage(page, true, url);
            }
        });

        // Handle breadcrumb and other navigation links
        $(document).on('click', 'a[data-ajax="true"]', function(e) {
            e.preventDefault();
            const url = $(this).attr('href');
            const page = $(this).data('page') || self.extractPageFromPath(url);
            self.loadPage(page, true, url);
        });
    },

    // Load page content via AJAX
    loadPage: function(page, pushState = true, customUrl = null) {
        const self = this;
        const url = customUrl || `${this.config.apiUrl}/${page}`;
        
        // Show loading state
        this.showPageLoading();
        
        // Update active navigation
        this.updateActiveNavigation(page);
        
        $.ajax({
            url: url,
            method: 'GET',
            data: { ajax: 1 },
            success: function(response) {
                self.handlePageResponse(response, page, pushState, url);
            },
            error: function(xhr, status, error) {
                self.handlePageError(xhr, page);
            }
        });
    },

    // Handle successful page response
    handlePageResponse: function(response, page, pushState, url) {
        const self = this;
        
        setTimeout(function() {
            if (typeof response === 'string') {
                // HTML response
                $(self.config.contentContainer).html(response);
            } else if (response.html) {
                // JSON response with HTML
                $(self.config.contentContainer).html(response.html);
                
                // Handle additional data
                if (response.title) {
                    document.title = response.title;
                }
                if (response.notifications) {
                    self.showNotifications(response.notifications);
                }
            }
            
            // Update browser history
            if (pushState) {
                const newUrl = url.replace('?ajax=1', '').replace('&ajax=1', '');
                history.pushState({page: page}, '', newUrl);
                self.config.pageHistory.push(page);
            }
            
            self.config.currentPage = page;
            self.hidePageLoading();
            self.reinitializePageComponents();
            
        }, self.config.loadingDelay);
    },

    // Handle page loading error
    handlePageError: function(xhr, page) {
        const self = this;
        let errorMessage = 'An error occurred while loading the page.';
        
        if (xhr.status === 404) {
            errorMessage = 'Page not found.';
        } else if (xhr.status === 403) {
            errorMessage = 'Access denied.';
        } else if (xhr.status === 500) {
            errorMessage = 'Server error occurred.';
        }
        
        setTimeout(function() {
            $(self.config.contentContainer).html(`
                <div class="error-container">
                    <div class="card-3d">
                        <div class="card-header">
                            <h4 class="text-danger">Error Loading Page</h4>
                        </div>
                        <div class="card-body text-center">
                            <i class="fas fa-exclamation-triangle text-danger mb-3" style="font-size: 3rem;"></i>
                            <p class="text-muted">${errorMessage}</p>
                            <button class="btn btn-primary" onclick="AchidasApp.loadPage('dashboard')">
                                <i class="fas fa-home"></i> Go to Dashboard
                            </button>
                        </div>
                    </div>
                </div>
            `);
            self.hidePageLoading();
        }, self.config.loadingDelay);
    },

    // Show page loading state
    showPageLoading: function() {
        const loadingHtml = `
            <div class="loading-container">
                <div class="loading-spinner">
                    <div class="spinner-3d">
                        <div class="cube1"></div>
                        <div class="cube2"></div>
                    </div>
                    <p class="loading-text">Loading...</p>
                </div>
            </div>
        `;
        $(this.config.contentContainer).html(loadingHtml);
    },

    // Hide page loading state
    hidePageLoading: function() {
        $('.loading-container').fadeOut(200);
    },

    // Update active navigation item
    updateActiveNavigation: function(page) {
        // Remove active class from all nav items
        $('.nav-link').removeClass('active');
        
        // Add active class to current page nav item
        $(`.nav-link[data-page="${page}"]`).addClass('active');
        
        // Update breadcrumb if exists
        this.updateBreadcrumb(page);
    },

    // Update breadcrumb
    updateBreadcrumb: function(page) {
        const breadcrumbMap = {
            'dashboard': 'Dashboard',
            'service/list': 'Services',
            'invoice/list': 'Invoices',
            'domain/list': 'Domains',
            'profile': 'Profile',
            'transactions': 'Transactions',
            'deposit/history': 'Deposit History'
        };
        
        const breadcrumbText = breadcrumbMap[page] || page.charAt(0).toUpperCase() + page.slice(1);
        $('.breadcrumb-item.active').text(breadcrumbText);
    },

    // Reinitialize components after page load
    reinitializePageComponents: function() {
        // Reinitialize tooltips
        $('[data-bs-toggle="tooltip"]').tooltip();
        
        // Reinitialize any charts or widgets
        this.initializeCharts();
        
        // Reinitialize form validation
        this.setupForms();
        
        // Reinitialize tables
        this.setupTables();
        
        // Trigger custom page loaded event
        $(document).trigger('pageLoaded', [this.config.currentPage]);
    },

    // Setup event listeners
    setupEventListeners: function() {
        const self = this;
        
        // Global click handler for buttons with loading states
        $(document).on('click', '.btn[data-loading]', function() {
            const $btn = $(this);
            const originalText = $btn.html();
            const loadingText = $btn.data('loading') || 'Loading...';
            
            $btn.html(`<i class="fas fa-spinner fa-spin"></i> ${loadingText}`).prop('disabled', true);
            
            // Re-enable after 3 seconds (fallback)
            setTimeout(function() {
                $btn.html(originalText).prop('disabled', false);
            }, 3000);
        });
    },

    // Setup notifications
    setupNotifications: function() {
        // Create notification container if it doesn't exist
        if (!$('#notification-container').length) {
            $('body').append('<div id="notification-container" class="notification-container"></div>');
        }
    },

    // Show notifications
    showNotifications: function(notifications) {
        const self = this;
        
        notifications.forEach(function(notification) {
            self.showNotification(notification.message, notification.type || 'info');
        });
    },

    // Show single notification
    showNotification: function(message, type = 'info') {
        const iconMap = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-circle',
            'warning': 'fas fa-exclamation-triangle',
            'info': 'fas fa-info-circle'
        };
        
        const notification = $(`
            <div class="notification notification-${type}">
                <i class="${iconMap[type]}"></i>
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `);
        
        $('#notification-container').append(notification);
        
        // Auto remove after duration
        setTimeout(function() {
            notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, this.config.notificationDuration);
        
        // Manual close
        notification.find('.notification-close').on('click', function() {
            notification.fadeOut(300, function() {
                $(this).remove();
            });
        });
    },

    // Setup modals
    setupModals: function() {
        // Handle modal forms with AJAX
        $(document).on('submit', '.modal form[data-ajax="true"]', function(e) {
            e.preventDefault();
            const $form = $(this);
            const $modal = $form.closest('.modal');

            AchidasApp.submitForm($form, function(response) {
                if (response.success) {
                    $modal.modal('hide');
                    AchidasApp.showNotification(response.message || 'Operation completed successfully', 'success');

                    // Reload current page if needed
                    if (response.reload) {
                        AchidasApp.loadPage(AchidasApp.config.currentPage, false);
                    }
                }
            });
        });
    },

    // Setup tables
    setupTables: function() {
        // Initialize DataTables if available
        if ($.fn.DataTable) {
            $('.data-table').each(function() {
                if (!$.fn.DataTable.isDataTable(this)) {
                    $(this).DataTable({
                        responsive: true,
                        pageLength: 25,
                        language: {
                            search: "Search:",
                            lengthMenu: "Show _MENU_ entries",
                            info: "Showing _START_ to _END_ of _TOTAL_ entries",
                            paginate: {
                                first: "First",
                                last: "Last",
                                next: "Next",
                                previous: "Previous"
                            }
                        }
                    });
                }
            });
        }
    },

    // Setup forms
    setupForms: function() {
        const self = this;

        // Handle AJAX form submissions
        $(document).on('submit', 'form[data-ajax="true"]', function(e) {
            e.preventDefault();
            self.submitForm($(this));
        });

        // Real-time form validation
        $(document).on('blur', 'input[required], select[required], textarea[required]', function() {
            self.validateField($(this));
        });
    },

    // Submit form via AJAX
    submitForm: function($form, callback) {
        const self = this;
        const url = $form.attr('action') || window.location.href;
        const method = $form.attr('method') || 'POST';
        const formData = new FormData($form[0]);

        // Show loading state
        const $submitBtn = $form.find('button[type="submit"]');
        const originalText = $submitBtn.html();
        $submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Processing...').prop('disabled', true);

        $.ajax({
            url: url,
            method: method,
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (callback) {
                    callback(response);
                } else {
                    self.handleFormResponse(response, $form);
                }
            },
            error: function(xhr) {
                self.handleFormError(xhr, $form);
            },
            complete: function() {
                $submitBtn.html(originalText).prop('disabled', false);
            }
        });
    },

    // Handle form response
    handleFormResponse: function(response, $form) {
        if (response.success) {
            this.showNotification(response.message || 'Form submitted successfully', 'success');

            if (response.redirect) {
                const page = this.extractPageFromPath(response.redirect);
                this.loadPage(page, true, response.redirect);
            } else if (response.reload) {
                this.loadPage(this.config.currentPage, false);
            }
        } else {
            this.showNotification(response.message || 'An error occurred', 'error');

            // Show field errors
            if (response.errors) {
                this.showFieldErrors($form, response.errors);
            }
        }
    },

    // Handle form error
    handleFormError: function(xhr, $form) {
        let message = 'An error occurred while submitting the form.';

        if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
            this.showFieldErrors($form, xhr.responseJSON.errors);
            message = 'Please correct the errors below.';
        }

        this.showNotification(message, 'error');
    },

    // Show field errors
    showFieldErrors: function($form, errors) {
        // Clear previous errors
        $form.find('.is-invalid').removeClass('is-invalid');
        $form.find('.invalid-feedback').remove();

        // Show new errors
        Object.keys(errors).forEach(function(field) {
            const $field = $form.find(`[name="${field}"]`);
            if ($field.length) {
                $field.addClass('is-invalid');
                $field.after(`<div class="invalid-feedback">${errors[field][0]}</div>`);
            }
        });
    },

    // Validate single field
    validateField: function($field) {
        const value = $field.val().trim();
        const isRequired = $field.prop('required');

        $field.removeClass('is-invalid is-valid');
        $field.siblings('.invalid-feedback').remove();

        if (isRequired && !value) {
            $field.addClass('is-invalid');
            $field.after('<div class="invalid-feedback">This field is required.</div>');
            return false;
        } else if (value) {
            $field.addClass('is-valid');
            return true;
        }

        return true;
    },

    // Initialize animations
    initializeAnimations: function() {
        // Smooth scrolling for anchor links
        $(document).on('click', 'a[href^="#"]', function(e) {
            const target = $($(this).attr('href'));
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 500);
            }
        });

        // Fade in animations for new content
        $(document).on('pageLoaded', function() {
            $('.card-3d, .stat-card').each(function(index) {
                $(this).css('opacity', '0').delay(index * 100).animate({opacity: 1}, 300);
            });
        });
    },

    // Initialize charts (placeholder for chart libraries)
    initializeCharts: function() {
        // Initialize Chart.js charts if available
        if (typeof Chart !== 'undefined') {
            $('.chart-canvas').each(function() {
                // Chart initialization logic here
            });
        }
    }
};

// Initialize when document is ready
$(document).ready(function() {
    AchidasApp.init();
});
