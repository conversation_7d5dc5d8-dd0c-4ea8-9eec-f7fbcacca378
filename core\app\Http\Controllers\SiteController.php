<?php

namespace App\Http\Controllers;

use App\DomainRegisters\Register;
use Carbon\Carbon;
use App\Models\Page;
use App\Models\Product;
use App\Models\Frontend;
use App\Models\Language;
use App\Models\Subscriber;
use App\Models\DomainSetup;
use Illuminate\Http\Request;
use App\Models\SupportTicket;
use App\Models\SupportMessage;
use App\Models\ServiceCategory;
use App\Models\AdminNotification;
use App\Models\DomainRegister;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Validator;

class SiteController extends Controller {

    public function index() {

        $reference = @$_GET['reference'];
        if ($reference) {
            session()->put('reference', $reference);
        }
        $pageTitle = 'Home';
        $sections = Page::where('tempname', $this->activeTemplate)->where('slug', '/')->first();
        return view($this->activeTemplate . 'home', compact('pageTitle', 'sections'));
    }

    public function pages($slug) {
        $page = Page::where('tempname', $this->activeTemplate)->where('slug', $slug)->firstOrFail();
        $pageTitle = $page->name;
        $sections = $page->secs;
        return view($this->activeTemplate . 'pages', compact('pageTitle', 'sections'));
    }

    public function contact() {
        $pageTitle = "Contact";
        $user = auth()->user();
        $sections = Page::where('tempname', $this->activeTemplate)->where('slug', 'contact')->first();
        return view($this->activeTemplate . 'contact', compact('pageTitle', 'sections', 'user'));
    }

    public function contactSubmit(Request $request) {
        
        $this->validate($request, [
            'name' => 'required',
            'email' => 'required',
            'subject' => 'required|string|max:255',
            'message' => 'required',
        ]);

        if (!verifyCaptcha()) {
            $notify[] = ['error', 'Invalid captcha provided'];
            return back()->withNotify($notify);
        }

        $request->session()->regenerateToken();

        $random = getNumber();

        $ticket = new SupportTicket();
        $ticket->user_id = auth()->id() ?? 0;
        $ticket->name = $request->name;
        $ticket->email = $request->email;
        $ticket->priority = 2;


        $ticket->ticket = $random;
        $ticket->subject = $request->subject;
        $ticket->last_reply = Carbon::now();
        $ticket->status = 0;
        $ticket->save();

        $adminNotification = new AdminNotification();
        $adminNotification->user_id = auth()->user() ? auth()->user()->id : 0;
        $adminNotification->title = 'A new contact message has been submitted';
        $adminNotification->click_url = urlPath('admin.ticket.view', $ticket->id);
        $adminNotification->save();

        $message = new SupportMessage();
        $message->support_ticket_id = $ticket->id;
        $message->message = $request->message;
        $message->save();

        $notify[] = ['success', 'Ticket created successfully!'];

        return to_route('ticket.view', [$ticket->ticket])->withNotify($notify);
    }

    public function policyPages($slug, $id) {
        $policy = Frontend::where('id', $id)->where('data_keys', 'policy_pages.element')->firstOrFail();
        $pageTitle = $policy->data_values->title;
        return view($this->activeTemplate . 'policy', compact('policy', 'pageTitle'));
    }

    public function changeLanguage($lang = null) {
        $language = Language::where('code', $lang)->first();
        if (!$language) $lang = 'en';
        session()->put('lang', $lang);
        return back();
    }

    public function cookieAccept() {
        Cookie::queue('gdpr_cookie',gs('site_name') , 43200);
    }

    public function cookiePolicy() {
        $pageTitle = 'Cookie Policy';
        $cookie = Frontend::where('data_keys', 'cookie.data')->first();
        return view($this->activeTemplate . 'cookie', compact('pageTitle', 'cookie'));
    }

    public function placeholderImage($size = null) {
        $imgWidth = explode('x', $size)[0];
        $imgHeight = explode('x', $size)[1];
        $text = $imgWidth . '×' . $imgHeight;
        $fontFile = realpath('assets/font/RobotoMono-Regular.ttf');
        $fontSize = round(($imgWidth - 50) / 8);
        if ($fontSize <= 9) {
            $fontSize = 9;
        }
        if ($imgHeight < 100 && $fontSize > 30) {
            $fontSize = 30;
        }

        $image     = imagecreatetruecolor($imgWidth, $imgHeight);
        $colorFill = imagecolorallocate($image, 100, 100, 100);
        $bgFill    = imagecolorallocate($image, 175, 175, 175);
        imagefill($image, 0, 0, $bgFill);
        $textBox = imagettfbbox($fontSize, 0, $fontFile, $text);
        $textWidth  = abs($textBox[4] - $textBox[0]);
        $textHeight = abs($textBox[5] - $textBox[1]);
        $textX      = ($imgWidth - $textWidth) / 2;
        $textY      = ($imgHeight + $textHeight) / 2;
        header('Content-Type: image/jpeg');
        imagettftext($image, $fontSize, 0, $textX, $textY, $colorFill, $fontFile, $text);
        imagejpeg($image);
        imagedestroy($image);
    }

    public function maintenance() {
        $pageTitle = 'Maintenance Mode';

        if (gs('maintenance_mode') == 0) {
            return to_route('home');
        }
        $maintenance = Frontend::where('data_keys', 'maintenance.data')->first();
        return view($this->activeTemplate . 'maintenance', compact('pageTitle', 'maintenance'));
    }

    public function subscribe(Request $request) {

        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255|unique:subscribers,email'
        ]);

        if (!$validator->passes()) {
            return response()->json(['error' => $validator->errors()->all()]);
        }

        $newSubscriber = new Subscriber();
        $newSubscriber->email = $request->email;
        $newSubscriber->save();

        return response()->json(['success' => true, 'message' => 'Thank you, we will notice you our latest news']);
    }

    public function blogs() {
        $pageTitle = 'Announcements';
        $sections = Page::where('tempname', $this->activeTemplate)->where('slug', 'announcements')->first();
        return view($this->activeTemplate . 'blogs', compact('pageTitle', 'sections'));
    }

    public function blogDetails($slug, $id) {
        $blog = Frontend::where('id', $id)->where('data_keys', 'blog.element')->firstOrFail();
        $pageTitle = $blog->data_values->title;
        return view($this->activeTemplate . 'blog_details', compact('blog', 'pageTitle'));
    }

    public function serviceCategory($slug = null) {

        $serviceCategory = ServiceCategory::active()->when($slug, function ($category) use ($slug) {
            $category->where('slug', $slug);
        })->firstOrFail();

        $pageTitle = $serviceCategory->name;
        return view($this->activeTemplate . 'service_category', compact('pageTitle', 'serviceCategory'));
    }

    public function productConfigure($categorySlug, $productSlug, $id) {

        $product = Product::active()->where('id', $id)->whereHas('serviceCategory', function ($category) {
            $category->active($category);
        })->whereHas('price', function ($price) {
            $price->filter($price);
        })
            ->with('getConfigs.activeGroup.activeOptions.activeSubOptions.getOnlyPrice')
            ->firstOrFail();

        $domains = [];
        $pageTitle = 'Product Configure';

        if ($product->domain_register) {
            $domains = DomainSetup::active()->orderBy('id', 'DESC')->with('pricing')->get();
        }

        return view($this->activeTemplate . 'product_configure', compact('product', 'pageTitle', 'domains'));
    } 

    public function registerDomain(Request $request) {

        setTimeLimit();

        $pageTitle = 'Register New Domain';
        $domain = strtolower($request->domain);
        $result = [];   

        if ($domain) { 
            $request->validate([
                'domain'=> ['regex:/^[a-zA-Z0-9.-]+$/']
            ]);

            $defaultDomainRegister = DomainRegister::getDefault();
            if (!$defaultDomainRegister) {
                $notify[] = ['info', 'There is no default domain register, please setup default domain register'];
                return redirect()->route('register.domain')->withNotify($notify);
            }
            $request->merge(['domain'=>$domain]);

            $register = new Register($defaultDomainRegister->alias); //The Register is a class
            $register->command = 'searchDomain';
            $register->domain = $domain;
            $execute = $register->run();

            if (!$execute['success']) {
                $notify = [];
                $messages = $execute['message'] ?? [];
                if (!is_array($messages)) {
                    $messages = [$messages];
                }
                foreach($messages as $message){
                    if ($message) {
                        $notify[] = ['error', $message];
                    }
                }
                return redirect()->route('register.domain')->withNotify($notify);
            }

            if (@$execute['data']['status'] == 'ERROR') {
                $notify[] = ['error', $execute['data']['message']];
                return redirect()->route('register.domain')->withNotify($notify);
            }

            $result = $execute;
        }

        return view($this->activeTemplate . 'register_domain', compact('pageTitle', 'result'));
    }

    public function searchDomain(Request $request) {

        setTimeLimit();

        $validator = Validator::make($request->all(), [
            'domain' => ['required', 'regex:/^[a-zA-Z0-9.-]+$/'],
            'tlds' => ['sometimes', 'array'],
            'include_pricing' => ['sometimes', 'boolean'],
            'include_alternatives' => ['sometimes', 'boolean'],
            'limit' => ['sometimes', 'integer', 'min:1', 'max:50']
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success'=>false,
                'message'=>$validator->errors()->all(),
            ]);
        }

        $domain = strtolower($request->domain);
        $includePricing = $request->get('include_pricing', true);
        $includeAlternatives = $request->get('include_alternatives', true);
        $limit = $request->get('limit', 10);

        // Get TLDs from request or use all active TLDs
        $requestedTlds = $request->get('tlds', []);
        if (empty($requestedTlds)) {
            $domainSetups = \App\Models\DomainSetup::active()->get(['extension']);
            $requestedTlds = $domainSetups->pluck('extension')->map(function($ext) {
                return substr($ext, 1); // Remove the dot
            })->toArray();
        }

        $defaultDomainRegister = DomainRegister::getDefault();
        if (!$defaultDomainRegister) {
            return response()->json(['success' => false, 'message' => 'There is no default domain register configured. Please contact the administrator to set up domain registration services.']);
        }

        // Check if there are any domain setups (TLDs) configured
        $domainSetupCount = \App\Models\DomainSetup::active()->count();
        if ($domainSetupCount === 0) {
            return response()->json(['success' => false, 'message' => 'No domain extensions (TLDs) are configured. Please contact the administrator to set up domain extensions like .com, .net, etc.']);
        }

        try {
            $register = new Register($defaultDomainRegister->alias); //The Register is a class
            $register->command = 'searchDomain';
            $register->domain = $domain;
            $execute = $register->run();

            if (!$execute['success']) {
                return response()->json(['success' => false, 'message' => $execute['message']]);
            }

            // Enhanced response with pricing and alternatives
            $searchResults = $this->processSearchResults($execute['data'], $includePricing, $includeAlternatives, $domain, $limit);

            return response()->json([
                'success' => true,
                'result' => [
                    'searchTerm' => $domain,
                    'results' => $searchResults['domains'],
                    'alternatives' => $searchResults['alternatives'],
                    'pricing_included' => $includePricing,
                    'alternatives_included' => $includeAlternatives,
                    'total_checked' => count($searchResults['domains']),
                    'available_count' => count(array_filter($searchResults['domains'], function($d) {
                        return $d['available'];
                    }))
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }

        if (@$execute['data']['status'] == 'ERROR') {
            return response()->json(['success' => false, 'message' => $execute['data']['message']]);
        }

        return response()->json(['success' =>true, 'result'=>$execute]);
    }

    private function processSearchResults($rawResults, $includePricing, $includeAlternatives, $originalDomain, $limit) {
        $domains = [];
        $alternatives = [];

        foreach ($rawResults as $domainData) {
            $tld = getTld($domainData['domain']);
            $pricing = null;

            if ($includePricing) {
                $domainSetup = \App\Models\DomainSetup::where('extension', $tld)->active()->with('pricing')->first();
                if ($domainSetup && $domainSetup->pricing) {
                    $pricing = [
                        'oneYear' => $domainSetup->pricing->oneYearPrice,
                        'twoYear' => $domainSetup->pricing->twoYearPrice,
                        'threeYear' => $domainSetup->pricing->threeYearPrice,
                        'fourYear' => $domainSetup->pricing->fourYearPrice,
                        'fiveYear' => $domainSetup->pricing->fiveYearPrice,
                        'sixYear' => $domainSetup->pricing->sixYearPrice,
                        'idProtection' => [
                            'oneYear' => $domainSetup->pricing->oneYearIdProtection,
                            'twoYear' => $domainSetup->pricing->twoYearIdProtection,
                            'threeYear' => $domainSetup->pricing->threeYearIdProtection,
                            'fourYear' => $domainSetup->pricing->fourYearIdProtection,
                            'fiveYear' => $domainSetup->pricing->fiveYearIdProtection,
                            'sixYear' => $domainSetup->pricing->sixYearIdProtection,
                        ]
                    ];
                }
            }

            $domains[] = [
                'domain' => $domainData['domain'],
                'tld' => $tld,
                'available' => $domainData['available'],
                'match' => $domainData['match'] ?? 0,
                'setup' => $domainData['setup'],
                'pricing' => $pricing
            ];
        }

        // Generate alternatives if requested
        if ($includeAlternatives && !empty($domains)) {
            $alternatives = $this->generateDomainAlternatives($originalDomain, $limit);
        }

        return [
            'domains' => $domains,
            'alternatives' => $alternatives
        ];
    }

    private function generateDomainAlternatives($domain, $limit) {
        $alternatives = [];
        $variations = [
            $domain . 'app',
            $domain . 'web',
            $domain . 'site',
            $domain . 'online',
            $domain . 'store',
            'my' . $domain,
            'get' . $domain,
            $domain . 'hub',
            $domain . 'zone',
            $domain . 'pro'
        ];

        $popularTlds = ['com', 'net', 'org', 'io', 'co'];

        foreach (array_slice($variations, 0, 3) as $variation) {
            foreach (array_slice($popularTlds, 0, 2) as $tld) {
                if (count($alternatives) >= $limit) break;

                // Check if this TLD is available in our system
                $domainSetup = \App\Models\DomainSetup::where('extension', '.' . $tld)->active()->with('pricing')->first();
                if ($domainSetup) {
                    $altDomain = $variation . '.' . $tld;

                    // For now, we'll mark alternatives as available
                    // In a real implementation, you'd check via API
                    $pricing = null;
                    if ($domainSetup->pricing) {
                        $pricing = [
                            'oneYear' => $domainSetup->pricing->oneYearPrice,
                            'twoYear' => $domainSetup->pricing->twoYearPrice,
                            'threeYear' => $domainSetup->pricing->threeYearPrice,
                        ];
                    }

                    $alternatives[] = [
                        'domain' => $altDomain,
                        'tld' => '.' . $tld,
                        'available' => true, // This should be checked via API in real implementation
                        'type' => 'alternative',
                        'setup' => $domainSetup,
                        'pricing' => $pricing
                    ];
                }
            }
            if (count($alternatives) >= $limit) break;
        }

        return $alternatives;
    }

    public function getDomainSuggestions(Request $request) {
        $validator = Validator::make($request->all(), [
            'keyword' => ['required', 'string', 'min:2', 'max:50'],
            'limit' => ['sometimes', 'integer', 'min:1', 'max:20']
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->all(),
            ]);
        }

        $keyword = strtolower($request->keyword);
        $limit = $request->get('limit', 10);

        $suggestions = $this->generateDomainAlternatives($keyword, $limit);

        return response()->json([
            'success' => true,
            'suggestions' => $suggestions,
            'keyword' => $keyword,
            'count' => count($suggestions)
        ]);
    }

    public function getPopularTlds() {
        $popularTlds = \App\Models\DomainSetup::active()
            ->with('pricing')
            ->orderBy('sort_order', 'asc')
            ->limit(10)
            ->get(['extension', 'id'])
            ->map(function($setup) {
                return [
                    'tld' => $setup->extension,
                    'id' => $setup->id,
                    'pricing' => $setup->pricing ? [
                        'oneYear' => $setup->pricing->oneYearPrice,
                        'twoYear' => $setup->pricing->twoYearPrice,
                    ] : null
                ];
            });

        return response()->json([
            'success' => true,
            'tlds' => $popularTlds
        ]);
    }

    public function validateDomain(Request $request) {
        $validator = Validator::make($request->all(), [
            'domain' => ['required', 'string']
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->all(),
            ]);
        }

        $domain = strtolower($request->domain);

        // Basic domain validation
        $isValid = preg_match('/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/', $domain);
        $tld = getTld($domain);
        $domainName = str_replace($tld, '', $domain);

        // Check if TLD is supported
        $tldSupported = \App\Models\DomainSetup::where('extension', $tld)->active()->exists();

        return response()->json([
            'success' => true,
            'valid' => $isValid && $tldSupported,
            'domain' => $domain,
            'tld' => $tld,
            'domain_name' => $domainName,
            'tld_supported' => $tldSupported,
            'format_valid' => $isValid
        ]);
    }

    public function bulkDomainSearch(Request $request) {
        $validator = Validator::make($request->all(), [
            'domains' => ['required', 'array', 'min:1', 'max:20'],
            'domains.*' => ['required', 'string', 'regex:/^[a-zA-Z0-9.-]+$/'],
            'include_pricing' => ['sometimes', 'boolean']
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->all(),
            ]);
        }

        $domains = array_map('strtolower', $request->domains);
        $includePricing = $request->get('include_pricing', true);
        $results = [];

        foreach ($domains as $domain) {
            try {
                $searchRequest = new Request([
                    'domain' => $domain,
                    'include_pricing' => $includePricing,
                    'include_alternatives' => false
                ]);

                $response = $this->searchDomain($searchRequest);
                $responseData = $response->getData(true);

                if ($responseData['success']) {
                    $results[] = [
                        'domain' => $domain,
                        'success' => true,
                        'data' => $responseData['result']
                    ];
                } else {
                    $results[] = [
                        'domain' => $domain,
                        'success' => false,
                        'message' => $responseData['message']
                    ];
                }
            } catch (\Exception $e) {
                $results[] = [
                    'domain' => $domain,
                    'success' => false,
                    'message' => $e->getMessage()
                ];
            }
        }

        return response()->json([
            'success' => true,
            'results' => $results,
            'total_domains' => count($domains),
            'successful_searches' => count(array_filter($results, function($r) { return $r['success']; }))
        ]);
    }

}
