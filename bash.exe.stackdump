Stack trace:
Frame         Function      Args
0007FFFF9B80  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9B80, 0007FFFF8A80) msys-2.0.dll+0x1FE8E
0007FFFF9B80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9E58) msys-2.0.dll+0x67F9
0007FFFF9B80  000210046832 (000210286019, 0007FFFF9A38, 0007FFFF9B80, 000000000000) msys-2.0.dll+0x6832
0007FFFF9B80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9B80  000210068E24 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9E60  00021006A225 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEEC3A0000 ntdll.dll
7FFEEB840000 KERNEL32.DLL
7FFEE9A80000 KERNELBASE.dll
7FFEEC0E0000 USER32.dll
7FFEEA110000 win32u.dll
7FFEEC0B0000 GDI32.dll
7FFEE9670000 gdi32full.dll
7FFEE9850000 msvcp_win.dll
7FFEE9FC0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEEA6B0000 advapi32.dll
7FFEEC2B0000 msvcrt.dll
7FFEEBDB0000 sechost.dll
7FFEEA360000 RPCRT4.dll
7FFEE8AF0000 CRYPTBASE.DLL
7FFEE97B0000 bcryptPrimitives.dll
7FFEEA780000 IMM32.DLL
