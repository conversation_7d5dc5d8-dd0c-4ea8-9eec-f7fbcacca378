<?php
    $getResponse = collect(@$result['data'] ?? []);
?>

<?php $__env->startSection('data'); ?>
    <div class="col-lg-9">
        <div class="row gy-4">
            <div class="col-lg-12"> 
                <h3><?php echo app('translator')->get('Register Domain'); ?></h3>
                <p class="mt-2"><?php echo app('translator')->get('Find your new domain name. Enter your desired domain name or keyword below to check availability'); ?>...</p>
            </div>
            <div class="col-lg-12 text-center">
                <?php echo $__env->make($activeTemplate . 'partials.domain_search_form', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>

            <div class="col-lg-12 text-center">
                <?php if($getResponse->where('domain', @$result['domain'])->where('available', true)->count()): ?>
                    <h3>
                        <?php echo app('translator')->get('Congratulations'); ?>! <span class="text--base"><?php echo e(@$result['domain']); ?> <?php echo app('translator')->get('is'); ?></span> <?php echo app('translator')->get('available'); ?>!
                    </h3>
                <?php elseif($getResponse->where('domain', @$result['domain'])->where('available', false)->count()): ?>
                    <h3>
                        <span class="text--danger"><?php echo e(@$result['domain']); ?> <?php echo app('translator')->get('is'); ?></span> <?php echo app('translator')->get('unavailable'); ?>!
                    </h3>
                <?php endif; ?>
            </div>

            <div class="col-lg-12 text-center">
                <?php if(!@$result['isSupported'] && @$result['domain']): ?>
                    <h3><?php echo app('translator')->get('We are not supporting '); ?> <span class="text--warning">(<?php echo e(@$result['tld']); ?>)</span> <?php echo app('translator')->get('right now'); ?></h3>
                <?php endif; ?> 
            </div>

            <div class="col-12">
                <?php if($getResponse->count() > 0): ?>
                    <?php $__currentLoopData = $getResponse->sortByDesc('match'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="domain-row">
                            <span>
                                <?php echo e(@$data['domain']); ?>

                            </span>
                            <div class="text-end">
                                <?php if(@$data['available']): ?>
                                    <span class="fw-bold text-end">
                                        <?php echo e($general->cur_sym); ?><?php echo e(showAmount(@$data['setup']->pricing->firstPrice['price'] ?? 0)); ?>

                                    </span>
                                    <form action="<?php echo e(route('shopping.cart.add.domain')); ?>" method="post" class="d-inline ms-2">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="domain" required value="<?php echo e(@$data['domain']); ?> ">

                                        <input type="hidden" name="domain_setup_id" required value="<?php echo e(@$data['setup']->id); ?>">
                                        <button class="btn btn--sm btn--base<?php echo e(@$data['domain'] != $result['domain'] ? '-outline' : null); ?>">
                                            <i class="la la-cart-plus"></i> <?php echo app('translator')->get('Add'); ?>
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <span class="text--info fw-bold"><?php echo app('translator')->get('Unavailable'); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <?php if(!isset($result) || !@$result['domain']): ?>
                        <div class="text-center">
                            <p class="text-muted"><?php echo app('translator')->get('Enter a domain name above to search for availability'); ?></p>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make($activeTemplate . 'layouts.side_bar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/register_domain.blade.php ENDPATH**/ ?>