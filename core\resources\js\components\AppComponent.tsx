import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { useAuthStore } from '../stores/authStore'
import Layout from './Layout'
import Dashboard from '../pages/Dashboard'
import HostingServices from '../pages/HostingServices'
import DomainManagement from '../pages/DomainManagement'
import Billing from '../pages/Billing'
import Support from '../pages/Support'
import Login from '../pages/Login'
import Register from '../pages/Register'

function AppComponent() {
  const { isAuthenticated } = useAuthStore()

  if (!isAuthenticated) {
    return (
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="*" element={<Login />} />
      </Routes>
    )
  }

  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/hosting" element={<HostingServices />} />
        <Route path="/domains" element={<DomainManagement />} />
        <Route path="/billing" element={<Billing />} />
        <Route path="/support" element={<Support />} />
        <Route path="*" element={<Dashboard />} />
      </Routes>
    </Layout>
  )
}

export default AppComponent
