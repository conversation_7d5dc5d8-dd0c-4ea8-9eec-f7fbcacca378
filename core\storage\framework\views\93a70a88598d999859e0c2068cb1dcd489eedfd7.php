<form action="" class="form" id="domain-search-form">
    <div class="form-group position-relative mb-0">
        <div class="domain-search-icon"><i class="fas fa-search"></i></div>
        <input class="form-control form--control" type="text" name="domain" required placeholder="<?php echo app('translator')->get('Domain name or keyword'); ?>" value="<?php echo e(@request()->domain); ?>" autocomplete="off">
        <div class="domain-search-icon-reset">
            <button class="btn btn--base" type="submit" id="search-btn">
                <span class="btn-text"><?php echo app('translator')->get('Search'); ?></span>
                <span class="btn-loading d-none">
                    <i class="fas fa-spinner fa-spin"></i> <?php echo app('translator')->get('Searching...'); ?>
                </span>
            </button>
        </div>
        <!-- Domain suggestions dropdown -->
        <div id="domain-suggestions" class="domain-suggestions-dropdown" style="display: none;"></div>
    </div>
</form>

<!-- Popular TLDs Section -->
<div class="popular-tlds-section mt-3">
    <div class="text-center mb-2">
        <small class="text-muted"><?php echo app('translator')->get('Popular extensions'); ?>: </small>
        <div id="popular-tlds-list" class="d-inline-flex flex-wrap gap-2">
            <!-- Popular TLDs will be loaded here -->
        </div>
    </div>
</div>

<!-- Domain Results Container -->
<div id="domain-results" class="mt-4" style="display: none;">
    <div id="domain-status-message" class="text-center mb-3"></div>
    <div id="domain-list"></div>
</div>

<?php $__env->startPush('style'); ?>
    <style>
        .domain-results-section {
            margin-bottom: 2rem;
        }

        .domain-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fff;
            transition: all 0.3s ease;
        }

        .domain-row:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-color: #007bff;
        }

        .domain-row.available {
            border-left: 4px solid #28a745;
        }

        .domain-row.unavailable {
            border-left: 4px solid #dc3545;
            opacity: 0.7;
        }

        .domain-row.alternative {
            border-left: 4px solid #17a2b8;
            background: #f8f9fa;
        }

        .domain-info {
            flex: 1;
        }

        .domain-name {
            font-weight: 600;
            font-size: 16px;
            display: block;
        }

        .domain-pricing {
            margin-top: 5px;
        }

        .price {
            color: #28a745;
            font-weight: 600;
            margin-right: 10px;
        }

        .privacy-price {
            color: #6c757d;
            font-size: 0.9em;
        }

        .alternative-badge {
            background: #17a2b8;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 10px;
        }

        .domain-alternatives-section {
            border-top: 1px solid #e0e0e0;
            padding-top: 1rem;
        }

        #domain-results {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .btn-loading .fa-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .search-stats {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-size: 0.9em;
            color: #6c757d;
        }

        .domain-suggestions-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-top: none;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
        }

        .suggestion-item {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s ease;
        }

        .suggestion-item:hover {
            background-color: #f8f9fa;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-domain {
            font-weight: 600;
            color: #333;
        }

        .suggestion-price {
            font-size: 0.9em;
            color: #28a745;
            float: right;
        }

        .form-group {
            position: relative;
        }

        .popular-tlds-section {
            margin-top: 15px;
        }

        .tld-badge {
            display: inline-block;
            padding: 4px 8px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 15px;
            font-size: 0.85em;
            color: #495057;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 2px;
        }

        .tld-badge:hover {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .tld-badge.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        (function($) {
            "use strict";

            var suggestionTimeout;
            var $domainInput = $('input[name="domain"]');
            var $suggestionsDropdown = $('#domain-suggestions');

            // Load popular TLDs on page load
            loadPopularTlds();

            // Real-time domain suggestions
            $domainInput.on('input', function() {
                var keyword = $(this).val().trim();

                clearTimeout(suggestionTimeout);

                if (keyword.length >= 2) {
                    suggestionTimeout = setTimeout(function() {
                        fetchDomainSuggestions(keyword);
                    }, 300);
                } else {
                    $suggestionsDropdown.hide();
                }
            });

            // Hide suggestions when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.form-group').length) {
                    $suggestionsDropdown.hide();
                }
            });

            // Handle suggestion clicks
            $(document).on('click', '.suggestion-item', function() {
                var domain = $(this).data('domain');
                $domainInput.val(domain);
                $suggestionsDropdown.hide();
                $('#domain-search-form').submit();
            });

            // Handle TLD badge clicks
            $(document).on('click', '.tld-badge', function() {
                var tld = $(this).data('tld');
                var currentValue = $domainInput.val().trim();

                // Remove existing TLD if present
                var domainName = currentValue.split('.')[0];

                // Set new domain with selected TLD
                $domainInput.val(domainName + tld);
                $('#domain-search-form').submit();
            });

            function fetchDomainSuggestions(keyword) {
                $.ajax({
                    url: "<?php echo e(route('api.domain.suggestions')); ?>",
                    method: 'GET',
                    data: {
                        keyword: keyword,
                        limit: 8
                    },
                    success: function(response) {
                        if (response.success && response.suggestions.length > 0) {
                            displaySuggestions(response.suggestions);
                        } else {
                            $suggestionsDropdown.hide();
                        }
                    },
                    error: function() {
                        $suggestionsDropdown.hide();
                    }
                });
            }

            function displaySuggestions(suggestions) {
                var html = '';
                suggestions.forEach(function(suggestion) {
                    html += '<div class="suggestion-item" data-domain="' + suggestion.domain + '">';
                    html += '<span class="suggestion-domain">' + suggestion.domain + '</span>';
                    if (suggestion.pricing && suggestion.pricing.oneYear) {
                        html += '<span class="suggestion-price"><?php echo e($general->cur_sym ?? "$"); ?>' + parseFloat(suggestion.pricing.oneYear).toFixed(2) + '</span>';
                    }
                    html += '</div>';
                });

                $suggestionsDropdown.html(html).show();
            }

            function loadPopularTlds() {
                $.ajax({
                    url: "<?php echo e(route('api.domain.popular-tlds')); ?>",
                    method: 'GET',
                    success: function(response) {
                        if (response.success && response.tlds.length > 0) {
                            displayPopularTlds(response.tlds);
                        }
                    },
                    error: function() {
                        // Fallback to default TLDs if API fails
                        var defaultTlds = [
                            {tld: '.com', pricing: null},
                            {tld: '.net', pricing: null},
                            {tld: '.org', pricing: null},
                            {tld: '.io', pricing: null},
                            {tld: '.co', pricing: null}
                        ];
                        displayPopularTlds(defaultTlds);
                    }
                });
            }

            function displayPopularTlds(tlds) {
                var html = '';
                tlds.slice(0, 8).forEach(function(tldData) {
                    html += '<span class="tld-badge" data-tld="' + tldData.tld + '">';
                    html += tldData.tld;
                    if (tldData.pricing && tldData.pricing.oneYear) {
                        html += ' <small><?php echo e($general->cur_sym ?? "$"); ?>' + parseFloat(tldData.pricing.oneYear).toFixed(0) + '</small>';
                    }
                    html += '</span>';
                });

                $('#popular-tlds-list').html(html);
            }

            $('#domain-search-form').on('submit', function(e) {
                e.preventDefault();

                var domain = $(this).find('input[name=domain]').val().trim();
                if (!domain) {
                    return;
                }

                // Show loading state
                var $btn = $('#search-btn');
                var $btnText = $btn.find('.btn-text');
                var $btnLoading = $btn.find('.btn-loading');

                $btn.prop('disabled', true);
                $btnText.addClass('d-none');
                $btnLoading.removeClass('d-none');

                // Clear previous results
                $('#domain-results').hide();
                $('#domain-status-message').empty();
                $('#domain-list').empty();

                // Enhanced AJAX request with more options
                $.ajax({
                    url: "<?php echo e(route('search.domain')); ?>",
                    method: 'POST',
                    data: {
                        domain: domain,
                        include_pricing: true,
                        include_alternatives: true,
                        limit: 20,
                        _token: "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(response) {
                        if (response.success) {
                            displayEnhancedDomainResults(response.result);
                        } else {
                            showError(response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = '<?php echo app('translator')->get("An error occurred while searching for domains. Please try again."); ?>';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        showError(errorMessage);
                    },
                    complete: function() {
                        // Reset button state
                        $btn.prop('disabled', false);
                        $btnText.removeClass('d-none');
                        $btnLoading.addClass('d-none');
                    }
                });
            });

            function displayEnhancedDomainResults(result) {
                var domains = result.results;
                var alternatives = result.alternatives || [];
                var searchTerm = result.searchTerm;

                // Show results container
                $('#domain-results').show();

                // Display search statistics
                var statsHtml = '<div class="search-stats">';
                statsHtml += '<?php echo app('translator')->get("Search Results"); ?>: ' + result.total_checked + ' <?php echo app('translator')->get("domains checked"); ?>, ';
                statsHtml += result.available_count + ' <?php echo app('translator')->get("available"); ?>';
                if (alternatives.length > 0) {
                    statsHtml += ', ' + alternatives.length + ' <?php echo app('translator')->get("alternatives suggested"); ?>';
                }
                statsHtml += '</div>';

                // Display status message
                var statusHtml = '';
                var exactMatch = domains.find(function(item) {
                    return item.domain === searchTerm + '.' + item.tld.replace('.', '');
                });

                if (exactMatch) {
                    if (exactMatch.available) {
                        statusHtml = '<h3><?php echo app('translator')->get("Congratulations"); ?>! <span class="text--base">' + exactMatch.domain + ' <?php echo app('translator')->get("is"); ?></span> <?php echo app('translator')->get("available"); ?>!</h3>';
                    } else {
                        statusHtml = '<h3><span class="text--danger">' + exactMatch.domain + ' <?php echo app('translator')->get("is"); ?></span> <?php echo app('translator')->get("unavailable"); ?>!</h3>';
                    }
                }

                $('#domain-status-message').html(statsHtml + statusHtml);

                // Display main results
                var domainsHtml = '<div class="domain-results-section">';
                domainsHtml += '<h4><?php echo app('translator')->get("Search Results"); ?></h4>';

                // Sort by match (searched domain first), then by availability
                domains.sort(function(a, b) {
                    if (b.match !== a.match) return (b.match || 0) - (a.match || 0);
                    return b.available - a.available;
                });

                domains.forEach(function(domainData) {
                    domainsHtml += '<div class="domain-row ' + (domainData.available ? 'available' : 'unavailable') + '">';
                    domainsHtml += '<div class="domain-info">';
                    domainsHtml += '<span class="domain-name">' + domainData.domain + '</span>';

                    // Show pricing if available
                    if (domainData.pricing && domainData.available) {
                        domainsHtml += '<div class="domain-pricing">';
                        domainsHtml += '<span class="price"><?php echo e($general->cur_sym ?? "$"); ?>' + parseFloat(domainData.pricing.oneYear).toFixed(2) + '/<?php echo app('translator')->get("year"); ?></span>';
                        if (domainData.pricing.idProtection.oneYear > 0) {
                            domainsHtml += '<span class="privacy-price">+<?php echo e($general->cur_sym ?? "$"); ?>' + parseFloat(domainData.pricing.idProtection.oneYear).toFixed(2) + ' <?php echo app('translator')->get("privacy"); ?></span>';
                        }
                        domainsHtml += '</div>';
                    }

                    domainsHtml += '</div>';
                    domainsHtml += '<div class="domain-actions">';

                    if (domainData.available && domainData.setup) {
                        var btnClass = domainData.match > 0 ? 'btn btn--sm btn--base' : 'btn btn--sm btn--base-outline';
                        domainsHtml += '<form action="<?php echo e(route("shopping.cart.add.domain")); ?>" method="post" style="display: inline;">';
                        domainsHtml += '<?php echo csrf_field(); ?>';
                        domainsHtml += '<input type="hidden" name="domain" value="' + domainData.domain + '">';
                        domainsHtml += '<input type="hidden" name="domain_setup_id" value="' + domainData.setup.id + '">';
                        domainsHtml += '<button type="submit" class="' + btnClass + '">';
                        domainsHtml += '<i class="la la-cart-plus"></i> <?php echo app('translator')->get("Add to Cart"); ?>';
                        domainsHtml += '</button>';
                        domainsHtml += '</form>';
                    } else {
                        domainsHtml += '<span class="text--info fw-bold"><?php echo app('translator')->get("Unavailable"); ?></span>';
                    }

                    domainsHtml += '</div>';
                    domainsHtml += '</div>';
                });

                domainsHtml += '</div>';

                // Display alternatives if available
                if (alternatives.length > 0) {
                    domainsHtml += '<div class="domain-alternatives-section mt-4">';
                    domainsHtml += '<h4><?php echo app('translator')->get("Alternative Suggestions"); ?></h4>';

                    alternatives.forEach(function(altData) {
                        domainsHtml += '<div class="domain-row alternative">';
                        domainsHtml += '<div class="domain-info">';
                        domainsHtml += '<span class="domain-name">' + altData.domain + '</span>';
                        domainsHtml += '<span class="alternative-badge"><?php echo app('translator')->get("Suggestion"); ?></span>';

                        if (altData.pricing) {
                            domainsHtml += '<div class="domain-pricing">';
                            domainsHtml += '<span class="price"><?php echo e($general->cur_sym ?? "$"); ?>' + parseFloat(altData.pricing.oneYear).toFixed(2) + '/<?php echo app('translator')->get("year"); ?></span>';
                            domainsHtml += '</div>';
                        }

                        domainsHtml += '</div>';
                        domainsHtml += '<div class="domain-actions">';

                        if (altData.available && altData.setup) {
                            domainsHtml += '<form action="<?php echo e(route("shopping.cart.add.domain")); ?>" method="post" style="display: inline;">';
                            domainsHtml += '<?php echo csrf_field(); ?>';
                            domainsHtml += '<input type="hidden" name="domain" value="' + altData.domain + '">';
                            domainsHtml += '<input type="hidden" name="domain_setup_id" value="' + altData.setup.id + '">';
                            domainsHtml += '<button type="submit" class="btn btn--sm btn--outline-base">';
                            domainsHtml += '<i class="la la-cart-plus"></i> <?php echo app('translator')->get("Add"); ?>';
                            domainsHtml += '</button>';
                            domainsHtml += '</form>';
                        }

                        domainsHtml += '</div>';
                        domainsHtml += '</div>';
                    });

                    domainsHtml += '</div>';
                }

                $('#domain-list').html(domainsHtml);
            }

            function showError(message) {
                $('#domain-results').show();
                var errorHtml = '<div class="alert alert-danger">';
                if (Array.isArray(message)) {
                    message.forEach(function(msg) {
                        errorHtml += '<p class="mb-0">' + msg + '</p>';
                    });
                } else {
                    errorHtml += '<p class="mb-0">' + message + '</p>';
                }
                errorHtml += '</div>';
                $('#domain-status-message').html(errorHtml);
            }

        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/partials/domain_search_form.blade.php ENDPATH**/ ?>